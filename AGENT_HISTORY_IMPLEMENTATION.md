# Agent History Saving Implementation

## Overview

This implementation adds functionality to save agent execution history to files with filenames that include the process_id. The history files are stored in an "agent_history" folder and follow a specific naming convention.

## Implementation Details

### 1. New Function: `save_agent_history_with_process_id`

**Location**: `app.py` (lines 823-858)

**Purpose**: Save agent execution history to a file with process_id in the filename.

**Parameters**:
- `history_data` (dict): The agent history data to save (must be JSON serializable)
- `process_id` (str): The unique process identifier

**Returns**: 
- `str`: The full path of the saved file

**Features**:
- Creates "agent_history" directory if it doesn't exist
- Generates timestamp in YYYYMMDD_HHMMSS format (UTC)
- Uses filename pattern: `agent_history_{timestamp}_{process_id}.json`
- Saves data in JSON format with proper encoding (UTF-8)
- Includes comprehensive error handling
- Provides detailed logging

### 2. Modified E2B Browser Use Module

**Location**: `e2b_browser_use.py`

**Changes Made**:

#### A. Enhanced `run_bedrock_task` function (lines 225-458):
- Added agent history tracking variable
- Modified bedrock script execution to capture agent history
- Created a modified bedrock script that saves history to `/tmp/agent_history.json`
- Updated return value to include agent history data
- Returns a dictionary with: `result`, `agent_history`, and `downloaded_files`

#### B. Modified bedrock script generation:
- Creates a dynamic bedrock script that captures and saves agent history
- Uses the browser_use Agent's `run()` method which returns `AgentHistoryList`
- Serializes the history using `model_dump()` method
- Saves history to a temporary file for retrieval

### 3. Modified Main Application Logic

**Location**: `app.py` (lines 952-994)

**Changes Made**:
- Added agent history extraction from e2b automation results
- Integrated history saving after successful e2b automation completion
- Added comprehensive logging for history saving process
- Includes error handling for history saving failures
- Maintains backward compatibility with existing functionality

### 4. Updated Bedrock Script

**Location**: `bedrock.py` (lines 90-95)

**Changes Made**:
- Modified `main()` function to return agent history
- Changed from `await agent.run(max_steps=30)` to `history = await agent.run(max_steps=30); return history`
- Added conditional execution to support both direct execution and import usage

## File Structure

```
/home/<USER>/10x-sales-agent/
├── app.py                          # Main FastAPI application with history saving
├── e2b_browser_use.py             # E2B automation with history capture
├── bedrock.py                     # Modified to return agent history
├── agent_history/                 # Directory for saved history files
│   └── agent_history_YYYYMMDD_HHMMSS_{process_id}.json
└── test_agent_history.py          # Test script for validation
```

## Filename Convention

History files follow this naming pattern:
```
agent_history_{timestamp}_{process_id}.json
```

Where:
- `timestamp`: UTC timestamp in YYYYMMDD_HHMMSS format
- `process_id`: Unique process identifier (UUID)

Example: `agent_history_20250730_145819_76651e41-685d-4d3d-8edf-a1bd16038720.json`

## JSON Structure

The saved history files contain the complete `AgentHistoryList` data structure from browser_use, including:

```json
{
  "history": [
    {
      "model_output": {
        "current_state": {
          "evaluation_previous_goal": "...",
          "memory": "...",
          "next_goal": "..."
        },
        "action": [...]
      },
      "result": [...],
      "state": {
        "url": "...",
        "title": "...",
        "tabs": [...],
        "screenshot": "...",
        "interacted_element": [...]
      },
      "metadata": {
        "step_start_time": 1640995200.0,
        "step_end_time": 1640995205.0,
        "input_tokens": 150,
        "step_number": 1
      }
    }
  ]
}
```

## Error Handling

The implementation includes comprehensive error handling:

1. **JSON Serialization Errors**: Catches non-serializable data and provides clear error messages
2. **File System Errors**: Handles directory creation and file writing failures
3. **Missing History Data**: Gracefully handles cases where no history is available
4. **Process Continuation**: History saving failures don't interrupt the main process flow

## Integration Points

### 1. Process Lifecycle Integration
- History saving occurs after successful e2b automation completion
- Integrated into the existing `_run_search_process` function
- Maintains process status and logging consistency

### 2. Logging Integration
- Uses existing process-specific loggers
- Provides detailed logging for debugging and monitoring
- Logs to multiple logger instances (main, process-specific, e2b-specific)

### 3. Backward Compatibility
- Existing functionality remains unchanged
- New history saving is additive, not disruptive
- Graceful degradation if history capture fails

## Testing

A comprehensive test suite is provided in `test_agent_history.py`:

### Test Coverage:
1. **Basic Functionality**: Tests successful history saving with valid data
2. **File Verification**: Confirms file creation, content accuracy, and naming convention
3. **Directory Structure**: Verifies correct directory creation and file placement
4. **Error Handling**: Tests behavior with non-serializable data
5. **Cleanup**: Ensures test files are properly cleaned up

### Running Tests:
```bash
python3 test_agent_history.py
```

## Usage Example

The functionality is automatically integrated into the existing search process:

1. Client makes a POST request to `/search` endpoint
2. Process is created with unique `process_id`
3. E2B automation runs with browser_use Agent
4. Agent execution history is captured during automation
5. History is automatically saved to `agent_history/agent_history_{timestamp}_{process_id}.json`
6. Process continues with normal CSV processing and response generation

## Benefits

1. **Debugging**: Complete execution history for troubleshooting failed searches
2. **Analytics**: Historical data for performance analysis and optimization
3. **Audit Trail**: Complete record of agent actions and decisions
4. **Reproducibility**: Ability to analyze and potentially replay agent behaviors
5. **Process Tracking**: Easy correlation between process_id and execution history

## Future Enhancements

Potential improvements for future versions:

1. **History Compression**: Implement compression for large history files
2. **Retention Policy**: Automatic cleanup of old history files
3. **History Analysis**: Tools for analyzing and visualizing agent behavior
4. **History Replay**: Functionality to replay agent actions from saved history
5. **Database Storage**: Option to store history in database instead of files
