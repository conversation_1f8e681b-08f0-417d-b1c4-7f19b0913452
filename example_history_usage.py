#!/usr/bin/env python3
"""
Example script showing how to load and analyze saved agent history files.

This script demonstrates how to:
1. Load agent history from saved JSON files
2. Extract useful information from the history
3. Analyze agent performance and behavior
"""

import json
import os
import glob
from datetime import datetime
from pathlib import Path

def load_agent_history(file_path: str) -> dict:
    """Load agent history from a JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading history from {file_path}: {e}")
        return None

def analyze_history(history_data: dict) -> dict:
    """Analyze agent history and extract key metrics."""
    if not history_data or 'history' not in history_data:
        return {}
    
    history_list = history_data['history']
    
    analysis = {
        'total_steps': len(history_list),
        'successful_steps': 0,
        'failed_steps': 0,
        'total_duration': 0.0,
        'total_tokens': 0,
        'actions_taken': [],
        'errors': [],
        'urls_visited': [],
        'final_result': None
    }
    
    for step in history_list:
        # Analyze metadata
        if 'metadata' in step and step['metadata']:
            metadata = step['metadata']
            if 'step_start_time' in metadata and 'step_end_time' in metadata:
                duration = metadata['step_end_time'] - metadata['step_start_time']
                analysis['total_duration'] += duration
            
            if 'input_tokens' in metadata:
                analysis['total_tokens'] += metadata['input_tokens']
        
        # Analyze results
        if 'result' in step and step['result']:
            for result in step['result']:
                if result.get('error'):
                    analysis['failed_steps'] += 1
                    analysis['errors'].append(result['error'])
                else:
                    analysis['successful_steps'] += 1
                
                # Check for final result
                if result.get('is_done') and result.get('extracted_content'):
                    analysis['final_result'] = result['extracted_content']
        
        # Analyze actions
        if 'model_output' in step and step['model_output'] and 'action' in step['model_output']:
            for action in step['model_output']['action']:
                action_type = list(action.keys())[0] if action else 'unknown'
                analysis['actions_taken'].append(action_type)
        
        # Analyze URLs
        if 'state' in step and step['state'] and 'url' in step['state']:
            url = step['state']['url']
            if url and url not in analysis['urls_visited']:
                analysis['urls_visited'].append(url)
    
    return analysis

def print_analysis(analysis: dict, file_path: str):
    """Print a formatted analysis report."""
    print(f"\n{'='*60}")
    print(f"AGENT HISTORY ANALYSIS: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    print(f"Total Steps: {analysis.get('total_steps', 0)}")
    print(f"Successful Steps: {analysis.get('successful_steps', 0)}")
    print(f"Failed Steps: {analysis.get('failed_steps', 0)}")
    print(f"Total Duration: {analysis.get('total_duration', 0):.2f} seconds")
    print(f"Total Tokens Used: {analysis.get('total_tokens', 0)}")
    
    if analysis.get('actions_taken'):
        print(f"\nActions Taken:")
        action_counts = {}
        for action in analysis['actions_taken']:
            action_counts[action] = action_counts.get(action, 0) + 1
        for action, count in action_counts.items():
            print(f"  - {action}: {count}")
    
    if analysis.get('urls_visited'):
        print(f"\nURLs Visited:")
        for url in analysis['urls_visited']:
            print(f"  - {url}")
    
    if analysis.get('errors'):
        print(f"\nErrors Encountered:")
        for error in analysis['errors']:
            print(f"  - {error}")
    
    if analysis.get('final_result'):
        print(f"\nFinal Result:")
        print(f"  {analysis['final_result']}")

def extract_process_info(filename: str) -> dict:
    """Extract timestamp and process_id from filename."""
    # Expected format: agent_history_YYYYMMDD_HHMMSS_process_id.json
    parts = filename.replace('.json', '').split('_')
    if len(parts) >= 4:
        date_part = parts[2]  # YYYYMMDD
        time_part = parts[3]  # HHMMSS
        process_id = '_'.join(parts[4:])  # process_id (may contain underscores)
        
        try:
            timestamp = datetime.strptime(f"{date_part}_{time_part}", "%Y%m%d_%H%M%S")
            return {
                'timestamp': timestamp,
                'process_id': process_id,
                'date': date_part,
                'time': time_part
            }
        except ValueError:
            pass
    
    return {}

def main():
    """Main function to analyze all agent history files."""
    history_dir = Path("agent_history")
    
    if not history_dir.exists():
        print("No agent_history directory found. No history files to analyze.")
        return
    
    # Find all history files
    history_files = glob.glob(str(history_dir / "agent_history_*.json"))
    
    if not history_files:
        print("No agent history files found in agent_history directory.")
        return
    
    print(f"Found {len(history_files)} agent history files to analyze.")
    
    # Sort files by timestamp (newest first)
    history_files.sort(reverse=True)
    
    total_analysis = {
        'total_files': len(history_files),
        'total_steps': 0,
        'total_duration': 0.0,
        'total_tokens': 0,
        'success_rate': 0.0
    }
    
    for file_path in history_files:
        print(f"\nAnalyzing: {file_path}")
        
        # Extract process info
        filename = os.path.basename(file_path)
        process_info = extract_process_info(filename)
        if process_info:
            print(f"Process ID: {process_info['process_id']}")
            print(f"Timestamp: {process_info['timestamp']}")
        
        # Load and analyze history
        history_data = load_agent_history(file_path)
        if history_data:
            analysis = analyze_history(history_data)
            print_analysis(analysis, file_path)
            
            # Add to total analysis
            total_analysis['total_steps'] += analysis.get('total_steps', 0)
            total_analysis['total_duration'] += analysis.get('total_duration', 0)
            total_analysis['total_tokens'] += analysis.get('total_tokens', 0)
        else:
            print(f"Failed to load history from {file_path}")
    
    # Print summary
    print(f"\n{'='*60}")
    print("SUMMARY ACROSS ALL FILES")
    print(f"{'='*60}")
    print(f"Total Files Analyzed: {total_analysis['total_files']}")
    print(f"Total Steps Across All Files: {total_analysis['total_steps']}")
    print(f"Total Duration Across All Files: {total_analysis['total_duration']:.2f} seconds")
    print(f"Total Tokens Used Across All Files: {total_analysis['total_tokens']}")
    
    if total_analysis['total_steps'] > 0:
        avg_duration_per_step = total_analysis['total_duration'] / total_analysis['total_steps']
        avg_tokens_per_step = total_analysis['total_tokens'] / total_analysis['total_steps']
        print(f"Average Duration Per Step: {avg_duration_per_step:.2f} seconds")
        print(f"Average Tokens Per Step: {avg_tokens_per_step:.1f}")

if __name__ == "__main__":
    main()
