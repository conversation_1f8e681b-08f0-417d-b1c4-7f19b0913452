"""
Seerfar Product Search API

This FastAPI application provides concurrent product search capabilities using E2B browser automation.
Key features:
- Concurrent request processing: Multiple /search requests are handled simultaneously using asyncio.to_thread()
- Process isolation: Each search runs in its own sandbox environment with unique process_id
- Thread-safe operations: Process manager uses async locks to prevent race conditions
- Background task execution: Requests return immediately with process_id while work continues asynchronously
"""

from fastapi import FastAPI, HTTPException, Security, Depends, Request
from fastapi.security import APIKeyHeader
from pydantic import BaseModel, SecretStr, field_validator
from typing import List, Optional, Dict, Any
import asyncio
import os
from datetime import datetime, UTC, timezone, timedelta
import pandas as pd
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
# Removed browser_use imports as we're replacing with e2b_browser_use
# from browser_use import Agent, Browser, BrowserConfig
# from browser_use.browser.context import BrowserContextConfig
import tempfile
import shutil
import glob
import json
import logging
import secrets
import multiprocessing
import uvicorn
import uuid
from pydantic import ConfigDict
from pathlib import Path
from prompts import QUERY_PARSER_PROMPT, BROWSER_SEARCH_PROMPT, SESSION_SUMMARY_PROMPT, format_query_parser_prompt, MIX_CATEGORY_PROMPT, KEYWORD_SEARCH_PROMPT, COMPARE_PRODUCT_TITLE
import traceback
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from path_manager import initialize_session_paths, get_current_downloads_path, get_current_processed_path
import re
import random
import string
import os.path
import base64
from pathlib import Path
import e2b_browser_use

def category_cleaner(category1, category2, category3):
    """
    Returns the last valid category from the given category hierarchy.
    A category is considered valid if it is not "All".
    
    Args:
        category1 (str): First level category
        category2 (str): Second level category
        category3 (str): Third level category
        
    Returns:
        str: The last valid category in the hierarchy
    """
    if category3 != "All":
        return category3
    elif category2 != "All":
        return category2
    else:
        return category1

# Configure logging with more detail
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# API Key security
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=True)

async def get_api_key(api_key_header: str = Security(api_key_header)) -> str:
    if api_key_header == os.getenv("FAST_API_KEY"):
        return api_key_header
    raise HTTPException(
        status_code=401,
        detail="Invalid or missing API Key"
    )

# Initialize FastAPI app
app = FastAPI(title="Seerfar Product Search API")

# Global variables to track the current operations
# Removed current_browser and current_agent as we're using e2b_browser_use
operation_stopped = False

# Test mode configuration
TEST_MODE = False  # Set to True to bypass download process
TEST_CSV_PATH = "/Users/<USER>/downloads/Seerfar-Product20250404_6.csv"  # Path to your test CSV file

# Create screenshots directory
SCREENSHOTS_DIR = os.path.join(os.path.expanduser('~'), 'screenshots')
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

def get_screenshot_path(process_id: str, step_number: int) -> str:
    """Generate a unique screenshot path for a given process and step."""
    timestamp = datetime.now(timezone(timedelta(hours=8))).strftime('%Y%m%d_%H%M%S')
    return os.path.join(SCREENSHOTS_DIR, f'{process_id}_step_{step_number}_{timestamp}.png')

# Pydantic models for request and response
class TextQueryRequest(BaseModel):
    query: str
    from_selection: bool = False
    category_1_item: Optional[List[str]] = None
    category_3_item: Optional[List[str]] = None
    
    @field_validator('query')
    def validate_query(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Query must be a non-empty string')
        return v.strip()

class ProductSearchRequest(BaseModel):
    keyword: str
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    record_count: int
    seller_type: str = "All"  # Default to "All" if not specified

class SalesInfo(BaseModel):
    count: int
    revenue: float
    growth_rate: float
    revenue_rate: float
    gross_margin: float

class PerformanceInfo(BaseModel):
    impressions: int
    product_card_views: int
    add_to_cart_rate: float
    order_conversion_rate: float
    advertising_cost_share: float
    return_cancellation_rate: float

class ProductDetails(BaseModel):
    variations: int
    ratings_count: int
    ratings_average: float
    launch_age: int
    weight: str
    volume: str

class SellerInfo(BaseModel):
    shop: str
    seller_type: str
    fulfillment: str

class Product(BaseModel):
    id: str
    image_url: str
    title: str
    listing_url: str
    sku: str
    brand: str
    categories: List[str]
    sales_method: str
    price: float
    sales: SalesInfo
    performance: PerformanceInfo
    product_details: ProductDetails
    seller_info: SellerInfo
    qa_count: int

class ProductSearchResponse(BaseModel):
    total_items: int
    timestamp: str
    products: List[Product]

# Process status models
class ProcessStatus(BaseModel):
    process_id: str
    status: str  # "running", "completed", "error", "stopped"
    start_time: datetime
    end_time: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[ProductSearchResponse] = None
    # Removed browser and agent fields as we're using e2b_browser_use instead

    model_config = ConfigDict(arbitrary_types_allowed=True)

class ProcessInfo(BaseModel):
    process_id: str
    status: str
    start_time: datetime
    end_time: Optional[datetime] = None
    error: Optional[str] = None

class GetResponseResult(BaseModel):
    status_code: int
    message: str
    data: Optional[ProductSearchResponse] = None

# Session Summary models
class SessionSummaryResponse(BaseModel):
    status_code: int
    message: str
    query: str
    summary: str

class ProductCheckRequest(BaseModel):
    title_1688: str
    title_ozon: str

class ProductCheckResponse(BaseModel):
    is_match: bool

def setup_process_logging(process_id: str) -> logging.Logger:
    """Set up logging for a specific process."""
    # Create logs directory in /tmp which is typically writable
    logs_dir = os.path.join('/tmp', 'seerfar_logs')
    os.makedirs(logs_dir, exist_ok=True)
    
    # Create a logger for this process
    process_logger = logging.getLogger(f'process_{process_id}')
    process_logger.setLevel(logging.INFO)  
    
    # Remove any existing handlers to avoid duplicate logs
    for handler in process_logger.handlers[:]:
        process_logger.removeHandler(handler)
    
    # Get current date and time for the filename
    current_time = datetime.now(timezone(timedelta(hours=8))).strftime('%Y%m%d_%H%M%S')
    
    # Create a file handler for this process with date and time at the beginning of filename
    log_file = os.path.join(logs_dir, f'{current_time}_{process_id}.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO) 
    
    # Create a formatter with more detailed information
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(process)d:%(thread)d] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    
    # Add the handler to the logger
    process_logger.addHandler(file_handler)
    
    # Also capture e2b_browser_use service logs
    e2b_logger = logging.getLogger('e2b_browser_use')
    e2b_logger.setLevel(logging.INFO)
    e2b_logger.addHandler(file_handler)
    
    # Ensure logs are flushed immediately
    file_handler.flush()
    
    return process_logger

# Process manager to handle multiple processes
class ProcessManager:
    def __init__(self):
        self.processes: Dict[str, ProcessStatus] = {}
        self.lock = asyncio.Lock()

    async def create_process(self) -> str:
        async with self.lock:
            process_id = str(uuid.uuid4())
            # Set up process-specific logging
            process_logger = setup_process_logging(process_id)
            process_logger.info(f"Process {process_id} created")
            
            self.processes[process_id] = ProcessStatus(
                process_id=process_id,
                status="running",
                start_time=datetime.now(timezone(timedelta(hours=8)))
            )
            return process_id

    async def update_process(self, process_id: str, status: str, error: Optional[str] = None, result: Optional[ProductSearchResponse] = None):
        async with self.lock:
            if process_id in self.processes:
                process = self.processes[process_id]
                process.status = status
                if error:
                    process.error = error
                if result:
                    process.result = result
                if status in ["completed", "error", "stopped"]:
                    process.end_time = datetime.now(timezone(timedelta(hours=8)))

                # Log the status update
                process_logger = logging.getLogger(f'process_{process_id}')
                process_logger.info(f"Process status updated to: {status}")
                if error:
                    process_logger.error(f"Process error: {error}")

    async def get_process(self, process_id: str) -> Optional[ProcessStatus]:
        async with self.lock:
            return self.processes.get(process_id)

    async def stop_process(self, process_id: str) -> bool:
        async with self.lock:
            if process_id in self.processes:
                process = self.processes[process_id]
                if process.status == "running":
                    # E2B automation handles its own cleanup
                    # No need to stop browser/agent as they're managed by e2b_browser_use
                    process.status = "stopped"
                    process.end_time = datetime.now(timezone(timedelta(hours=8)))
                    return True
        return False

# Initialize process manager
process_manager = ProcessManager()

@app.post("/stop/{process_id}")
async def stop_process(process_id: str, api_key: str = Depends(get_api_key)):
    success = await process_manager.stop_process(process_id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Process {process_id} not found or already stopped"
        )
    return {"message": f"Process {process_id} stopped successfully"}

@app.get("/get_response/{process_id}", response_model=GetResponseResult)
async def get_response(process_id: str, api_key: str = Depends(get_api_key)):
    try:
        logger.info(f"Getting response for process ID: {process_id}")
        process = await process_manager.get_process(process_id)
        
        if not process:
            logger.warning(f"Process {process_id} not found")
            return GetResponseResult(
                status_code=404,
                message=f"Process {process_id} not found"
            )
        
        logger.info(f"Process status: {process.status}")
        
        if process.status == "running":
            return GetResponseResult(
                status_code=202,
                message="Process is still running"
            )
        
        if process.status == "error":
            logger.error(f"Process error: {process.error}")
            return GetResponseResult(
                status_code=500,
                message=process.error or "An error occurred during processing"
            )
        
        if process.status == "stopped":
            return GetResponseResult(
                status_code=499,
                message="Process was stopped by user"
            )
        
        if process.status == "completed" and process.result:
            logger.info("Process completed successfully")
            return GetResponseResult(
                status_code=200,
                message="Process completed successfully",
                data=process.result
            )
        
        logger.error("Unexpected process state")
        return GetResponseResult(
            status_code=500,
            message="Unexpected process state"
        )
    except Exception as e:
        logger.error(f"Error in get_response: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_browser_search(username: str, password: str, request: ProductSearchRequest):
    if TEST_MODE:
        return TEST_CSV_PATH

    # Get current timestamp before starting the task
    start_time = datetime.now(timezone(timedelta(hours=8)))

    try:
        # Format the prompt for the e2b browser use main function
        prompt = BROWSER_SEARCH_PROMPT.format(
            username=username,
            password=password,
            min_price=request.min_price,
            max_price=request.max_price,
            category1=request.category1,
            category2=request.category2,
            category3=request.category3,
            seller_type=request.seller_type,
            last_valid_category=request.last_valid_category
        )

        logger.info(f"Running e2b browser automation with prompt: {prompt[:100]}...")

        # Call the main function from e2b_browser_use with the prompt as query
        results = e2b_browser_use.main(query=prompt)

        logger.info(f"E2B browser automation completed with results: {results}")

        # Find CSV files created after the start time
        logger.info("Looking for downloaded CSV file")
        # Use current session downloads path
        try:
            downloads_path = get_current_downloads_path()
        except RuntimeError:
            # Fallback to legacy path if no session is active
            downloads_path = os.path.join(os.path.expanduser('/home/<USER>/10x-sales-agent'), 'downloads')
        csv_files = []

        # Look for any CSV files created during this run (not just Seerfar-Product pattern)
        for file in glob.glob(os.path.join(downloads_path, "*.csv")):
            # Skip files in processed subdirectory
            if 'processed' in file:
                continue
            file_time = datetime.fromtimestamp(os.path.getctime(file), timezone(timedelta(hours=8)))
            if file_time >= start_time:
                csv_files.append(file)

        if not csv_files:
            # If no files created during this run, look for any recent CSV files
            logger.info("No CSV files created during this run, looking for any recent CSV files...")
            all_csv_files = []
            for file in glob.glob(os.path.join(downloads_path, "*.csv")):
                if 'processed' not in file:
                    all_csv_files.append(file)

            if all_csv_files:
                # Get the most recent CSV file
                latest_file = max(all_csv_files, key=os.path.getctime)
                logger.info(f"Found recent CSV file: {latest_file}")
                return latest_file
            else:
                raise Exception("No CSV file found in downloads directory")

        # Get the most recent file from the filtered list
        latest_file = max(csv_files, key=os.path.getctime)
        logger.info(f"Found CSV file: {latest_file}")

        # Move CSV file to processed directory after finding it
        try:
            # Use current session processed path
            try:
                processed_dir = get_current_processed_path()
            except RuntimeError:
                # Fallback to legacy processed path
                processed_dir = os.path.join(downloads_path, "processed")
                os.makedirs(processed_dir, exist_ok=True)

            filename = os.path.basename(latest_file)
            processed_file_path = os.path.join(processed_dir, filename)

            # Check if file already exists in processed directory
            if os.path.exists(processed_file_path):
                # Add timestamp to avoid conflicts
                import time
                timestamp = int(time.time())
                name, ext = os.path.splitext(filename)
                new_filename = f"{name}_{timestamp}{ext}"
                processed_file_path = os.path.join(processed_dir, new_filename)
                logger.info(f"File exists in processed, using new name: {new_filename}")

            # Move the file to processed directory
            import shutil
            shutil.move(latest_file, processed_file_path)
            logger.info(f"Moved CSV file to processed directory: {processed_file_path}")

            # Return the new path in processed directory
            return processed_file_path

        except Exception as e:
            logger.error(f"Error moving CSV file to processed directory: {str(e)}")
            # Return original path if move fails
            return latest_file

    except Exception as e:
        logger.error(f"Error in e2b browser automation: {str(e)}")
        raise e

def process_csv_to_json(csv_path: str) -> ProductSearchResponse:
    try:
        # Read the CSV file
        df = pd.read_csv(csv_path)
        
        # Print column names for debugging
        print("CSV Columns:", df.columns.tolist())
        
        # Helper function to safely convert to integer
        def safe_int(value):
            if pd.isna(value):
                return 0
            try:
                return int(float(value))
            except (ValueError, TypeError):
                return 0
        
        # Convert DataFrame to list of Product objects
        products = []
        for index, row in df.iterrows():
            try:
                # Clean up price and revenue values by removing currency symbol and converting to float
                price = float(str(row['Price']).replace('₽', '').strip())
                revenue = float(str(row['Revenue']).replace('₽', '').strip())
                
                # Clean up percentage values by removing % symbol and converting to float
                def clean_percentage(value):
                    if pd.isna(value):
                        return 0.0
                    return float(str(value).replace('%', '').strip()) / 100
                
                # Extract launch age in months
                launch_age_parts = str(row['Launch Age']).split('\n')
                if len(launch_age_parts) > 1:
                    # Extract age from the second part
                    age_parts = launch_age_parts[1].split()
                    if len(age_parts) >= 2:
                        if 'Years' in age_parts[1]:
                            years = int(age_parts[0])
                            months = int(age_parts[2]) if len(age_parts) > 3 else 0
                            launch_age_months = years * 12 + months
                        elif 'Months' in age_parts[1]:
                            launch_age_months = int(age_parts[0])
                        else:
                            launch_age_months = 0
                    else:
                        launch_age_months = 0
                else:
                    launch_age_months = 0
                
                # Handle NaN values in Brand and other fields
                brand = str(row['Brand']) if pd.notna(row['Brand']) else ""
                weight = str(row['Weight']) if pd.notna(row['Weight']) else "N/A"
                volume = str(row['Volume']) if pd.notna(row['Volume']) else "N/A"
                
                product = Product(
                    id=str(row['SKU']),  # Using SKU as the ID
                    image_url=row['Image'],
                    title=row['Title'],
                    listing_url=row['Listing URL'],
                    sku=str(row['SKU']),
                    brand=brand,
                    categories=[cat.strip() for cat in str(row['Categories']).split('\n')],
                    sales_method=str(row['sales method']),
                    price=price,
                    sales=SalesInfo(
                        count=safe_int(row['Sales']),
                        revenue=revenue,
                        growth_rate=clean_percentage(row['Sales Growth Rate']),
                        revenue_rate=clean_percentage(row['Revenue Rate']),
                        gross_margin=clean_percentage(row['Gross Margin'])
                    ),
                    performance=PerformanceInfo(
                        impressions=safe_int(row['Impressions']),
                        product_card_views=safe_int(row['Product Card Views']),
                        add_to_cart_rate=clean_percentage(row['Add-to-Cart Rate']),
                        order_conversion_rate=clean_percentage(row['Order conversion rate']),
                        advertising_cost_share=clean_percentage(row['Advertising cost share']),
                        return_cancellation_rate=clean_percentage(row['Return cancellation rate'])
                    ),
                    product_details=ProductDetails(
                        variations=safe_int(row['Variations']),
                        ratings_count=safe_int(row['NO. Ratings']),
                        ratings_average=float(row['Ratings']) if pd.notna(row['Ratings']) else 0.0,
                        launch_age=launch_age_months,  # Now using months as the unit
                        weight=weight,
                        volume=volume
                    ),
                    seller_info=SellerInfo(
                        shop=str(row['Shop']) if pd.notna(row['Shop']) else "",
                        seller_type=str(row['Seller type']) if pd.notna(row['Seller type']) else "",
                        fulfillment=str(row['Fulfillment']) if pd.notna(row['Fulfillment']) else ""
                    ),
                    qa_count=safe_int(row['Q&A'])
                )
                products.append(product)
            except Exception as e:
                print(f"Error processing row {index}: {e}")
                continue  # Skip this row and continue with the next one
        
        if not products:
            raise Exception("No valid products found in the CSV file")
            
        return ProductSearchResponse(
            total_items=len(products),
            timestamp=datetime.now(timezone(timedelta(hours=8))).isoformat(),
            products=products
        )
    except Exception as e:
        raise Exception(f"Error processing CSV file: {str(e)}")

async def parse_query_with_llm(query: str, from_selection: bool = False) -> ProductSearchRequest:
    """Parse the text query using LLM to extract search parameters."""
    llm = ChatOpenAI(
        base_url='https://openrouter.ai/api/v1',
        #model='google/gemini-2.0-flash-001',
        model='google/gemini-2.0-flash-lite-001',
        temperature=0,
        api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
    )
    
    try:
        # Format the prompt with the query
        formatted_prompt = format_query_parser_prompt(query)
        
        # Get response from LLM
        response = await llm.ainvoke(formatted_prompt)
        
        # Convert response to string and clean it
        if isinstance(response.content, (list, dict)):
            content = json.dumps(response.content)
        else:
            content = str(response.content).strip()
        
        logger.info(f"Raw LLM Response: {content}")
        
        # Clean and validate JSON
        try:
            # Extract JSON object if response contains extra text
            import re
            json_match = re.search(r'\{[^{}]*\}', content)
            if json_match:
                content = json_match.group()
            
            logger.info(f"Cleaned content: {content}")
            parsed_data = json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {str(e)}")
            logger.error(f"Problematic JSON string: {content}")
            raise ValueError(f"Invalid JSON format: {str(e)}")
        
        logger.info(f"Parsed JSON: {json.dumps(parsed_data, indent=2)}")
        
        # Validate required fields and types
        required_fields = {
            'keyword': str,
            'min_price': (int, float),
            'max_price': (int, float),
            'record_count': int,
            'seller_type': str
        }
        
        # Clean and validate each field
        for field, expected_type in required_fields.items():
            # Handle missing fields
            if field not in parsed_data:
                if field == 'seller_type':
                    parsed_data[field] = "All"
                elif field == 'keyword':
                    parsed_data[field] = query  # Use the original query as keyword if not provided
                elif field in ['min_price', 'max_price']:
                    parsed_data[field] = None
                else:
                    parsed_data[field] = 0
                continue
                
            value = parsed_data[field]
            
            # Clean string values
            if expected_type == str:
                # Ensure proper string formatting
                if not isinstance(value, str):
                    value = str(value)
                # Remove any invalid characters and ensure proper quotes
                value = value.strip().replace('\n', ' ').replace('\r', '')
                if value == "":
                    if field == 'seller_type':
                        value = "All"
                    elif field == 'keyword':
                        value = query  # Use the original query as keyword if empty
                    else:
                        value = "0"
                parsed_data[field] = value
            
            # Handle numeric values
            elif expected_type in [(int, float), int]:
                try:
                    if isinstance(value, str):
                        value = value.strip().replace(',', '').replace(' ', '')
                        if value.lower() == 'none' or value == '':
                            parsed_data[field] = None
                            continue
                    value = float(value)
                    if expected_type == int:
                        value = int(value)
                    parsed_data[field] = value
                except (ValueError, TypeError):
                    parsed_data[field] = None
        
        # Ensure numeric fields are non-negative if they are not None
        for field in ['min_price', 'max_price', 'record_count']:
            if parsed_data[field] is not None:
                parsed_data[field] = max(0, float(parsed_data[field]))
        
        # Ensure min_price <= max_price if both are not None
        if parsed_data['min_price'] is not None and parsed_data['max_price'] is not None:
            if parsed_data['min_price'] > parsed_data['max_price']:
                parsed_data['min_price'], parsed_data['max_price'] = parsed_data['max_price'], parsed_data['min_price']
        
        # Validate seller_type
        valid_seller_types = ["All", "Cross-border sellers", "Domestic seller"]
        if parsed_data['seller_type'] not in valid_seller_types:
            parsed_data['seller_type'] = "All"
        
        # If from_selection is True, we don't need to parse categories
        if not from_selection:
            # No need to handle categories anymore since we're using keyword
            pass
        else:
            # Set default values for categories when from_selection is True
            parsed_data['category1'] = "All"
            parsed_data['category2'] = "All"
            parsed_data['category3'] = "All"
            parsed_data['last_valid_category'] = "All"
        
        logger.info(f"Validated parameters: {json.dumps(parsed_data, indent=2)}")
        
        return ProductSearchRequest(**parsed_data)
    except Exception as e:
        logger.error(f"Error parsing query: {str(e)}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to parse query: {str(e)}"
        )

@app.post("/search", response_model=ProcessInfo)
async def search_products(
    request: Request,
    text_query: TextQueryRequest,
    api_key: str = Depends(get_api_key)
):
    try:
        # Log the incoming request
        body = await request.body()
        logger.info(f"Received search request: {body.decode()}")
        
        # Validate the query
        query = text_query.query
        logger.info(f"Validated query: {query}")
        
        username = os.getenv("SEERFAR_USERNAME")
        password = os.getenv("SEERFAR_PASSWORD")
        
        if not username or not password:
            logger.error("SEERFAR credentials not configured")
            raise HTTPException(status_code=500, detail="SEERFAR credentials not configured")
        
        # Create a new process
        process_id = await process_manager.create_process()
        logger.info(f"Created new process with ID: {process_id}")
        
        # Start the search process in the background
        asyncio.create_task(_run_search_process(process_id, username, password, text_query))
        logger.info(f"Started background search process for ID: {process_id}")
        
        # Return process information immediately
        process = await process_manager.get_process(process_id)
        logger.info(f"Returning process info for ID: {process_id}")
        return ProcessInfo(
            process_id=process.process_id,
            status=process.status,
            start_time=process.start_time
        )
    except Exception as e:
        logger.error(f"Error in search_products: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

def translate_categories_to_english(chinese_categories: List[str]) -> List[str]:
    """
    Translate Chinese categories to English using category_cleaned.json.
    Only return a list with the first exactly matched category translation, or an empty list if no exact match is found.
    Args:
        chinese_categories (List[str]): List of Chinese category names or paths
    Returns:
        List[str]: List with one translated English category name (if exactly matched), or empty list
    """
    try:
        # Load the category mapping from JSON file
        json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'category_cleaned.json')
        logger.info(f"Loading category mapping from: {json_path}")
        with open(json_path, 'r', encoding='utf-8') as f:
            category_data = json.load(f)
        def extract_categories(category_list):
            categories = []
            for category in category_list:
                if isinstance(category, dict):
                    if 'cnTitle' in category and 'enTitle' in category:
                        categories.append((category['cnTitle'], category['enTitle']))
                    if 'children' in category:
                        categories.extend(extract_categories(category['children']))
            return categories
        all_categories = extract_categories(category_data.get('oz', []))
        cn_to_en = dict(all_categories)
        logger.info(f"Created mapping with {len(cn_to_en)} Chinese to English translations")
        # Try to find the first exact match
        for category in chinese_categories:
            logger.info(f"Translating category: {category}")
            # Handle category paths (e.g., "家电/技术秤")
            if '/' in category:
                last_category = category.split('/')[-1]
            else:
                last_category = category
            if last_category in cn_to_en:
                translated = cn_to_en[last_category]
                logger.info(f"Found translation for last category: {last_category} -> {translated}")
                return [translated]
        # No exact match found
        logger.warning("No exact match found for any category.")
        return []
    except Exception as e:
        logger.error(f"Error translating categories: {str(e)}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return []  # Return empty list if translation fails

# Removed check_successful_download function as it's specific to browser_use logging
# E2B automation handles its own success/failure detection

def save_agent_history_with_process_id(history_data: dict, process_id: str) -> str:
    """
    Save agent execution history to a file with process_id in the filename.

    Args:
        history_data (dict): The agent history data to save (should be JSON serializable)
        process_id (str): The unique process identifier

    Returns:
        str: The full path of the saved file

    Raises:
        Exception: If there's an error saving the file
    """
    try:
        # Create agent_history directory if it doesn't exist
        history_dir = Path("agent_history")
        history_dir.mkdir(exist_ok=True)

        # Generate timestamp in YYYYMMDD_HHMMSS format (GMT+8)
        timestamp = datetime.now(timezone(timedelta(hours=8))).strftime("%Y%m%d_%H%M%S")

        # Create filename with pattern: agent_history_{timestamp}_{process_id}.json
        filename = f"agent_history_{timestamp}_{process_id}.json"
        filepath = history_dir / filename

        # Save the history data to JSON file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Agent history saved successfully to: {filepath}")

        # Generate summary asynchronously after saving the history file
        asyncio.create_task(generate_agent_summary_async(str(filepath), process_id))

        return str(filepath)

    except Exception as e:
        error_msg = f"Error saving agent history: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)

async def generate_agent_summary_async(history_file_path: str, process_id: str) -> None:
    """
    Generate a comprehensive summary of agent history data asynchronously.

    Args:
        history_file_path (str): Path to the agent history JSON file
        process_id (str): The unique process identifier
    """
    try:
        logger.info(f"Starting agent summary generation for process {process_id}")

        # Read the agent history file
        with open(history_file_path, 'r', encoding='utf-8') as f:
            history_data = json.load(f)

        # Extract summary data
        summary_data = extract_summary_data(history_data, process_id)

        # Create agent_summary directory if it doesn't exist
        summary_dir = Path("agent_summary")
        summary_dir.mkdir(exist_ok=True)

        # Generate timestamp for summary file
        timestamp = datetime.now(timezone(timedelta(hours=8))).strftime("%Y%m%d_%H%M%S")
        summary_filename = f"agent_summary_{timestamp}_{process_id}.json"
        summary_filepath = summary_dir / summary_filename

        # Save the summary data to JSON file
        with open(summary_filepath, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Agent summary saved successfully to: {summary_filepath}")

    except Exception as e:
        logger.error(f"Error generating agent summary for process {process_id}: {str(e)}")
        # Don't raise the exception to avoid blocking the main application flow

def extract_summary_data(history_data: dict, process_id: str) -> dict:
    """
    Extract summary data from agent history.

    Args:
        history_data (dict): The complete agent history data
        process_id (str): The unique process identifier

    Returns:
        dict: Summary data structure
    """
    try:
        history_list = history_data.get('history', [])

        if not history_list:
            logger.warning(f"No history data found for process {process_id}")
            return create_empty_summary(process_id)

        # Extract timing and token data
        total_input_tokens = 0
        step_start_times = []
        step_end_times = []
        keywords_extracted = []

        for step in history_list:
            # Extract metadata
            metadata = step.get('metadata', {})
            if metadata:
                # Sum input tokens
                input_tokens = metadata.get('input_tokens', 0)
                total_input_tokens += input_tokens

                # Collect timing data
                step_start_time = metadata.get('step_start_time')
                step_end_time = metadata.get('step_end_time')

                if step_start_time:
                    step_start_times.append(step_start_time)
                if step_end_time:
                    step_end_times.append(step_end_time)

            # Extract keywords from model output actions
            model_output = step.get('model_output', {})
            if model_output:
                actions = model_output.get('action', [])
                if isinstance(actions, list):
                    for action in actions:
                        if isinstance(action, dict):
                            # Extract text from various action types
                            for action_type, action_data in action.items():
                                if isinstance(action_data, dict) and 'text' in action_data:
                                    text_content = action_data['text']
                                    if text_content and isinstance(text_content, str):
                                        # Extract meaningful keywords (simple approach)
                                        words = extract_keywords_from_text(text_content)
                                        keywords_extracted.extend(words)

        # Calculate session timing
        session_start_time = min(step_start_times) if step_start_times else None
        session_end_time = max(step_end_times) if step_end_times else None

        # Calculate total duration
        total_duration_seconds = 0
        if session_start_time and session_end_time:
            total_duration_seconds = session_end_time - session_start_time

        # Convert timestamps to ISO format
        session_start_iso = datetime.fromtimestamp(session_start_time, timezone(timedelta(hours=8))).isoformat() if session_start_time else None
        session_end_iso = datetime.fromtimestamp(session_end_time, timezone(timedelta(hours=8))).isoformat() if session_end_time else None
        summary_timestamp_iso = datetime.now(timezone(timedelta(hours=8))).isoformat()

        # Extract final result from the last step
        final_result = None
        if history_list:
            last_step = history_list[-1]
            result_data = last_step.get('result', [])
            if result_data and isinstance(result_data, list) and len(result_data) > 0:
                final_result_item = result_data[0]
                if isinstance(final_result_item, dict):
                    final_result = {
                        "is_done": final_result_item.get("is_done", False),
                        "success": final_result_item.get("success", None),
                        "extracted_content": final_result_item.get("extracted_content", "")
                    }

        # Remove duplicates and limit keywords
        unique_keywords = list(set(keywords_extracted))[:10]  # Limit to 10 unique keywords

        return {
            "process_id": process_id,
            "total_input_tokens": total_input_tokens,
            "session_start_time": session_start_iso,
            "session_end_time": session_end_iso,
            "total_duration_seconds": round(total_duration_seconds, 2),
            "total_steps": len(history_list),
            "keywords_extracted": unique_keywords,
            "final_result": final_result,
            "summary_timestamp": summary_timestamp_iso
        }

    except Exception as e:
        logger.error(f"Error extracting summary data: {str(e)}")
        return create_empty_summary(process_id)

def extract_keywords_from_text(text: str) -> list:
    """
    Extract meaningful keywords from text content.

    Args:
        text (str): Text content to extract keywords from

    Returns:
        list: List of extracted keywords
    """
    if not text or not isinstance(text, str):
        return []

    # Simple keyword extraction - split by common delimiters and filter
    import re

    # Remove common stop words and extract meaningful terms
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}

    # Extract words (including non-English characters for Russian keywords)
    words = re.findall(r'\b[\w\u0400-\u04FF]+\b', text.lower())

    # Filter out stop words and short words
    keywords = [word for word in words if len(word) > 2 and word not in stop_words]

    return keywords[:5]  # Limit to 5 keywords per text

def create_empty_summary(process_id: str) -> dict:
    """
    Create an empty summary structure when no data is available.

    Args:
        process_id (str): The unique process identifier

    Returns:
        dict: Empty summary data structure
    """
    return {
        "process_id": process_id,
        "total_input_tokens": 0,
        "session_start_time": None,
        "session_end_time": None,
        "total_duration_seconds": 0,
        "total_steps": 0,
        "keywords_extracted": [],
        "final_result": None,
        "summary_timestamp": datetime.now(timezone(timedelta(hours=8))).isoformat()
    }

async def _run_search_process(process_id: str, username: str, password: str, text_query: TextQueryRequest):
    try:
        process_logger = logging.getLogger(f'process_{process_id}')
        e2b_logger = logging.getLogger('e2b_browser_use')

        logger.info(f"Starting search process for ID: {process_id}")
        process_logger.info(f"Starting search process for ID: {process_id}")
        e2b_logger.info(f"Starting search process for ID: {process_id}")
        
        # Parse the text query into structured parameters
        logger.info("Parsing query with LLM")
        process_logger.info("Parsing query with LLM")
        e2b_logger.info("Parsing query with LLM")

        if text_query.from_selection:
            # Translate categories to English
            translated_category_1_items = translate_categories_to_english(text_query.category_1_item or [])
            translated_category_3_items = translate_categories_to_english(text_query.category_3_item or [])

            logger.info(f"Translated category_1_items: {translated_category_1_items}")
            logger.info(f"Translated category_3_items: {translated_category_3_items}")

            # Use MIX_CATEGORY_PROMPT for selection-based search
            search_params = await parse_query_with_llm(text_query.query, True)
            logger.info(f"Parsed search parameters: {search_params}")
            process_logger.info(f"Parsed search parameters: {search_params}")
            e2b_logger.info(f"Parsed search parameters: {search_params}")
        else:
            # Use KEYWORD_SEARCH_PROMPT for keyword-based search
            search_params = await parse_query_with_llm(text_query.query, False)
            logger.info(f"Parsed search parameters: {search_params}")
            process_logger.info(f"Parsed search parameters: {search_params}")
            e2b_logger.info(f"Parsed search parameters: {search_params}")
        
        # Initialize session paths for this process
        logger.info(f"Initializing session paths for process ID: {process_id}")
        process_logger.info(f"Initializing session paths for process ID: {process_id}")
        e2b_logger.info(f"Initializing session paths for process ID: {process_id}")

        downloads_path, processed_path = initialize_session_paths(process_id)
        logger.info(f"Session downloads path: {downloads_path}")
        logger.info(f"Session processed path: {processed_path}")
        process_logger.info(f"Session downloads path: {downloads_path}")
        process_logger.info(f"Session processed path: {processed_path}")
        e2b_logger.info(f"Session downloads path: {downloads_path}")
        e2b_logger.info(f"Session processed path: {processed_path}")

        logger.info("Preparing e2b browser automation")
        process_logger.info("Preparing e2b browser automation")
        e2b_logger.info("Preparing e2b browser automation")

        try:
            logger.info("Creating prompt for e2b automation")
            process_logger.info("Creating prompt for e2b automation")
            e2b_logger.info("Creating prompt for e2b automation")

            # Choose the appropriate prompt based on from_selection
            if text_query.from_selection:
                prompt = MIX_CATEGORY_PROMPT.format(
                    username=username,
                    password=password,
                    min_price=search_params.min_price,
                    max_price=search_params.max_price,
                    category_1_items=translated_category_1_items,
                    category_3_items=translated_category_3_items,
                    seller_type=search_params.seller_type
                )
            else:
                prompt = KEYWORD_SEARCH_PROMPT.format(
                    username=username,
                    password=password,
                    keyword=search_params.keyword,
                    seller_type=search_params.seller_type
                )

            logger.info(f"Running e2b browser automation with prompt: {prompt[:100]}...")
            process_logger.info(f"Running e2b browser automation with prompt: {prompt[:100]}...")
            e2b_logger.info(f"Running e2b browser automation with prompt: {prompt[:100]}...")

            # Update process status
            await process_manager.update_process(process_id, "running")

            logger.info("Executing e2b main function")
            process_logger.info("Executing e2b main function")
            e2b_logger.info("Executing e2b main function")

            # Call the main function from e2b_browser_use with the prompt as query and process_id
            # Use asyncio.to_thread() to run the blocking call in a separate thread for concurrency
            logger.info("Running e2b_browser_use.main() in thread pool for concurrent execution")
            process_logger.info("Running e2b_browser_use.main() in thread pool for concurrent execution")
            e2b_logger.info("Running e2b_browser_use.main() in thread pool for concurrent execution")

            results = await asyncio.to_thread(e2b_browser_use.main, query=prompt, process_id=process_id)

            logger.info(f"E2B automation completed with results: {results}")
            process_logger.info(f"E2B automation completed with results: {results}")
            e2b_logger.info(f"E2B automation completed with results: {results}")

            # Save agent history if available
            try:
                if results and len(results) > 0:
                    # Extract the first result (bedrock task result)
                    bedrock_result = results[0]
                    if isinstance(bedrock_result, dict) and "agent_history" in bedrock_result:
                        agent_history = bedrock_result["agent_history"]
                        if agent_history:
                            logger.info("Saving agent execution history...")
                            process_logger.info("Saving agent execution history...")
                            e2b_logger.info("Saving agent execution history...")

                            # Save the agent history with process_id
                            history_file_path = save_agent_history_with_process_id(agent_history, process_id)

                            logger.info(f"Agent history saved to: {history_file_path}")
                            process_logger.info(f"Agent history saved to: {history_file_path}")
                            e2b_logger.info(f"Agent history saved to: {history_file_path}")
                        else:
                            logger.warning("No agent history data available to save")
                            process_logger.warning("No agent history data available to save")
                            e2b_logger.warning("No agent history data available to save")
                    else:
                        logger.warning("Results do not contain agent history information")
                        process_logger.warning("Results do not contain agent history information")
                        e2b_logger.warning("Results do not contain agent history information")
                else:
                    logger.warning("No results available from e2b automation")
                    process_logger.warning("No results available from e2b automation")
                    e2b_logger.warning("No results available from e2b automation")
            except Exception as history_error:
                logger.error(f"Error saving agent history: {str(history_error)}")
                process_logger.error(f"Error saving agent history: {str(history_error)}")
                e2b_logger.error(f"Error saving agent history: {str(history_error)}")

            # Check if process was stopped
            process = await process_manager.get_process(process_id)
            if process and process.status == "stopped":
                logger.info("Process was stopped")
                process_logger.info("Process was stopped")
                e2b_logger.info("Process was stopped")
                return

            # Find the most recently downloaded CSV file
            logger.info("Looking for downloaded CSV file")
            process_logger.info("Looking for downloaded CSV file")
            e2b_logger.info("Looking for downloaded CSV file")

            # Look for any CSV files (not just Seerfar-Product pattern)
            csv_files = []
            all_files_found = glob.glob(os.path.join(downloads_path, "*.csv"))
            logger.info(f"DEBUG: Found {len(all_files_found)} total CSV files in downloads_path")
            process_logger.info(f"DEBUG: Found {len(all_files_found)} total CSV files in downloads_path")

            for file in all_files_found:
                logger.info(f"DEBUG: Checking file: {file}")
                process_logger.info(f"DEBUG: Checking file: {file}")
                # Skip files in processed subdirectory
                if 'processed' not in file:
                    csv_files.append(file)
                    logger.info(f"DEBUG: Added file to csv_files: {file}")
                    process_logger.info(f"DEBUG: Added file to csv_files: {file}")
                else:
                    logger.info(f"DEBUG: Skipped processed file: {file}")
                    process_logger.info(f"DEBUG: Skipped processed file: {file}")

            logger.info(f"DEBUG: Found {len(csv_files)} CSV files in main downloads directory")
            process_logger.info(f"DEBUG: Found {len(csv_files)} CSV files in main downloads directory")

            # If no CSV files in main directory, look in processed directory
            if not csv_files:
                logger.info("No CSV files in main downloads directory, checking processed directory")
                process_logger.info("No CSV files in main downloads directory, checking processed directory")
                e2b_logger.info("No CSV files in main downloads directory, checking processed directory")

                # Use current session processed path
                try:
                    processed_dir = get_current_processed_path()
                except RuntimeError:
                    # Fallback to legacy processed path
                    processed_dir = os.path.join(downloads_path, "processed")

                if os.path.exists(processed_dir):
                    processed_files = glob.glob(os.path.join(processed_dir, "*.csv"))
                    logger.info(f"DEBUG: Found {len(processed_files)} CSV files in processed directory")
                    process_logger.info(f"DEBUG: Found {len(processed_files)} CSV files in processed directory")

                    if processed_files:
                        # Get the most recent processed file
                        latest_processed = max(processed_files, key=os.path.getctime)
                        logger.info(f"DEBUG: Most recent processed file: {latest_processed}")
                        process_logger.info(f"DEBUG: Most recent processed file: {latest_processed}")

                        # Copy it back to main downloads directory for processing
                        import shutil
                        filename = os.path.basename(latest_processed)
                        copied_file = os.path.join(downloads_path, f"processing_{filename}")
                        shutil.copy2(latest_processed, copied_file)

                        logger.info(f"Copied processed file back for API processing: {copied_file}")
                        process_logger.info(f"Copied processed file back for API processing: {copied_file}")
                        e2b_logger.info(f"Copied processed file back for API processing: {copied_file}")

                        latest_file = copied_file
                    else:
                        error_msg = "No CSV files found in downloads or processed directories"
                        logger.error(error_msg)
                        process_logger.error(error_msg)
                        e2b_logger.error(error_msg)
                        raise Exception(error_msg)
                else:
                    error_msg = "No CSV file found in downloads directory and processed directory does not exist"
                    logger.error(error_msg)
                    process_logger.error(error_msg)
                    e2b_logger.error(error_msg)
                    raise Exception(error_msg)
            else:
                # Get the most recent file from main directory
                latest_file = max(csv_files, key=os.path.getctime)
                logger.info(f"Found CSV file in main directory: {latest_file}")
                process_logger.info(f"Found CSV file in main directory: {latest_file}")
                e2b_logger.info(f"Found CSV file in main directory: {latest_file}")
            
            # Process the downloaded CSV file
            logger.info("Processing CSV file")
            process_logger.info("Processing CSV file")
            e2b_logger.info("Processing CSV file")
            response = process_csv_to_json(latest_file)

            # Clean up the CSV file after successful processing
            try:
                filename = os.path.basename(latest_file)

                # If this was a copied file from processed directory, just delete it
                if filename.startswith("processing_"):
                    os.remove(latest_file)
                    logger.info(f"Removed temporary processing file: {latest_file}")
                    process_logger.info(f"Removed temporary processing file: {latest_file}")
                    e2b_logger.info(f"Removed temporary processing file: {latest_file}")
                else:
                    # This was a new file, move it to processed directory
                    # Use current session processed path
                    try:
                        processed_dir = get_current_processed_path()
                    except RuntimeError:
                        # Fallback to legacy processed path
                        processed_dir = os.path.join(downloads_path, "processed")
                        os.makedirs(processed_dir, exist_ok=True)

                    processed_file_path = os.path.join(processed_dir, filename)

                    # Check if file already exists in processed directory
                    if os.path.exists(processed_file_path):
                        # Add timestamp to avoid conflicts
                        import time
                        timestamp = int(time.time())
                        name, ext = os.path.splitext(filename)
                        new_filename = f"{name}_{timestamp}{ext}"
                        processed_file_path = os.path.join(processed_dir, new_filename)
                        logger.info(f"File exists in processed, using new name: {new_filename}")
                        process_logger.info(f"File exists in processed, using new name: {new_filename}")

                    # Move the file to processed directory
                    shutil.move(latest_file, processed_file_path)
                    logger.info(f"Moved CSV file to processed directory: {processed_file_path}")
                    process_logger.info(f"Moved CSV file to processed directory: {processed_file_path}")
                    e2b_logger.info(f"Moved CSV file to processed directory: {processed_file_path}")
            except Exception as e:
                logger.error(f"Error cleaning up CSV file: {str(e)}")
                process_logger.error(f"Error cleaning up CSV file: {str(e)}")
                e2b_logger.error(f"Error cleaning up CSV file: {str(e)}")

            # Update process with result
            logger.info("Updating process with result")
            process_logger.info("Updating process with result")
            e2b_logger.info("Updating process with result")
            await process_manager.update_process(process_id, "completed", result=response)

        except Exception as e:
            error_message = str(e)
            logger.error(f"Error in e2b automation execution: {error_message}\n{traceback.format_exc()}")
            process_logger.error(f"Error in e2b automation execution: {error_message}\n{traceback.format_exc()}")
            e2b_logger.error(f"Error in e2b automation execution: {error_message}\n{traceback.format_exc()}")
            if "Operation was stopped by user" in error_message:
                await process_manager.update_process(process_id, "stopped", error=error_message)
            else:
                await process_manager.update_process(process_id, "error", error=error_message)
        finally:
            # Clean up resources (e2b handles its own cleanup)
            logger.info("E2B automation cleanup completed")
            process_logger.info("E2B automation cleanup completed")
            e2b_logger.info("E2B automation cleanup completed")

            # Ensure all logs are flushed
            for handler in process_logger.handlers:
                handler.flush()
            for handler in e2b_logger.handlers:
                handler.flush()
    except Exception as e:
        error_message = str(e)
        logger.error(f"Error in _run_search_process: {error_message}\n{traceback.format_exc()}")
        process_logger.error(f"Error in _run_search_process: {error_message}\n{traceback.format_exc()}")
        e2b_logger.error(f"Error in _run_search_process: {error_message}\n{traceback.format_exc()}")
        await process_manager.update_process(process_id, "error", error=error_message)

        # Ensure all logs are flushed
        for handler in process_logger.handlers:
            handler.flush()
        for handler in e2b_logger.handlers:
            handler.flush()

async def summarize_query_with_llm(query: str) -> str:
    """Summarize the text query using LLM to provide a simple text summary."""
    llm = ChatOpenAI(
        base_url='https://openrouter.ai/api/v1',
        #model='openai/gpt-4o-2024-11-20',
       # model='google/gemini-2.0-flash-lite-001',
        model='openai/gpt-4.1-nano',
        temperature=0,
        api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
    )
    
    prompt = SESSION_SUMMARY_PROMPT.format(query=query)
    
    try:
        response = await llm.ainvoke(prompt)
        # Get the response content as string
        summary = str(response.content).strip()
        logger.info(f"LLM Summary Response: {summary}")
        
        if not summary:
            raise ValueError("Empty summary received from LLM")
            
        return summary
    except Exception as e:
        logger.error(f"Error summarizing query: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Failed to summarize query: {str(e)}")

@app.post("/session_summary", response_model=SessionSummaryResponse)
async def get_session_summary(
    request: TextQueryRequest,
    api_key: str = Depends(get_api_key)
):
    try:
        logger.info(f"Received summary request with query: {request.query}")
        summary = await summarize_query_with_llm(request.query)
        return SessionSummaryResponse(
            status_code=200,
            message="Summary generated successfully",
            query=request.query,
            summary=summary
        )
    except Exception as e:
        logger.error(f"Error in get_session_summary: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/product_check", response_model=ProductCheckResponse)
def product_check(
    request: ProductCheckRequest,
    api_key: str = Depends(get_api_key)
):
    try:
        # Log the incoming request details
        logger.info(f"POST /product_check - Request: title_1688='{request.title_1688}', title_ozon='{request.title_ozon}'")

        llm = ChatOpenAI(
            base_url='https://openrouter.ai/api/v1',
            model='openai/gpt-4.1-mini',
            #model='openai/gpt-4.1-nano',
            temperature=0,
            api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
        )
        prompt = COMPARE_PRODUCT_TITLE.format(title1=request.title_1688, title2=request.title_ozon)
        response = llm.invoke(prompt)
        result = str(response.content).strip().lower()
        is_match = result == 'true'

        # Create response object
        response_data = ProductCheckResponse(is_match=is_match)

        # Log the response data
        logger.info(f"POST /product_check - Response: {response_data.model_dump_json()}")

        return response_data
    except Exception as e:
        logger.error(f"Error in product_check: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.middleware("http")
async def validate_json_middleware(request: Request, call_next):
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            # Try to read and parse the body as JSON
            body = await request.body()
            if body:
                try:
                    body_str = body.decode()
                    logger.info(f"Received request body: {body_str}")
                    # Attempt to parse JSON to validate it
                    json.loads(body_str)
                except json.JSONDecodeError as e:
                    return JSONResponse(
                        status_code=400,
                        content={
                            "detail": [{
                                "type": "json_invalid",
                                "loc": ["body"],
                                "msg": str(e),
                                "input": body_str
                            }]
                        }
                    )
                except Exception as e:
                    logger.error(f"Error processing request body: {e}")
                    return JSONResponse(
                        status_code=400,
                        content={
                            "detail": [{
                                "type": "json_invalid",
                                "loc": ["body"],
                                "msg": "Invalid request body",
                                "input": str(e)
                            }]
                        }
                    )
        except Exception as e:
            logger.error(f"Error in JSON validation middleware: {e}")
    
    response = await call_next(request)
    return response

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000) 