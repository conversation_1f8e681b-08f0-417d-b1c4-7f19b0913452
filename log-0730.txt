Jul 30 11:55:56 VM-4-13-ubuntu python[4034682]: INFO:     ************:38692 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:55:57 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:55:57,410 - __main__ - INFO - Received request body: {"title_1688":"新品小米手环9膜水凝膜定位秒贴膜神器手环膜适用小米手环10贴膜","title_ozon":"Xiao<PERSON> Фитнес-браслет Smart Band 10, черный"}
Jul 30 11:55:57 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:55:57,411 - __main__ - INFO - POST /product_check - Request: title_1688='新品小米手环9膜水凝膜定位秒贴膜神器手环膜适用小米手环10贴膜', title_ozon='Xiao<PERSON> Фитнес-браслет Smart Band 10, черный'
Jul 30 11:55:57 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:55:57,984 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:55:58 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:55:58,034 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:55:58 VM-4-13-ubuntu python[4034682]: INFO:     ************:38692 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:01,137 - __main__ - INFO - Received request body: {"title_1688":"适用vivox100陶瓷膜x90pro曲屏x80全屏x70手机x60t贴膜x50x30pro+","title_ozon":"Poco Смартфон POCO M5s NFC MT KG 95 Восьмиядерная 64-мегапиксельная четырехъядерная камера 6,43 \"AMOLED точечный дисплей 33 Вт EU 8/256 ГБ, серый"}
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:01,138 - __main__ - INFO - POST /product_check - Request: title_1688='适用vivox100陶瓷膜x90pro曲屏x80全屏x70手机x60t贴膜x50x30pro+', title_ozon='Poco Смартфон POCO M5s NFC MT KG 95 Восьмиядерная 64-мегапиксельная четырехъядерная камера 6,43 "AMOLED точечный дисплей 33 Вт EU 8/256 ГБ, серый'
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:01,635 - __main__ - INFO - Received request body: {"title_1688":"5V3528RGB灯带USB灯带 TV电视背景灯2835USB灯带 跑马灯带套装","title_ozon":"Hartens Телевизор HTY-43U11S-VD 43\" 4K UHD, серебристый"}
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:01,641 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:01,642 - __main__ - INFO - POST /product_check - Request: title_1688='5V3528RGB灯带USB灯带 TV电视背景灯2835USB灯带 跑马灯带套装', title_ozon='Hartens Телевизор HTY-43U11S-VD 43" 4K UHD, серебристый'
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:01,799 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:01 VM-4-13-ubuntu python[4034682]: INFO:     ************:38692 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:02 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:02,235 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:02 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:02,278 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:02 VM-4-13-ubuntu python[4034682]: INFO:     ************:38702 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:06 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:06,679 - __main__ - INFO - Received request body: {"title_1688":"适用OPPOReno14手机壳新款reno14pro金属镜头全包防摔磨砂保护套P","title_ozon":"realme Смартфон Note 60X Ростест (EAC) 3/64 ГБ, черный"}
Jul 30 11:56:06 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:06,680 - __main__ - INFO - POST /product_check - Request: title_1688='适用OPPOReno14手机壳新款reno14pro金属镜头全包防摔磨砂保护套P', title_ozon='realme Смартфон Note 60X Ростест (EAC) 3/64 ГБ, черный'
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:07,074 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:07,099 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: INFO:     ************:38702 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:07,426 - __main__ - INFO - Received request body: {"title_1688":"White Whisper Noise Sleep Sound Machine蓝牙音响白噪音助睡眠","title_ozon":"Яндекс Станция Мини с часами с Алисой Умная колонка на YaGPT, синий сапфир"}
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:07,427 - __main__ - INFO - POST /product_check - Request: title_1688='White Whisper Noise Sleep Sound Machine蓝牙音响白噪音助睡眠', title_ozon='Яндекс Станция Мини с часами с Алисой Умная колонка на YaGPT, синий сапфир'
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:07,784 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:07,803 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:07 VM-4-13-ubuntu python[4034682]: INFO:     ************:38702 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:20,020 - __main__ - INFO - Received request body: {"title_1688":"电视机液晶55寸50寸43寸60寸65寸32寸智能wifi网络家用电包邮牌","title_ozon":"Samsung Телевизор UE50DU7100UX 50\" 4K UHD, черный"}
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:20,021 - __main__ - INFO - POST /product_check - Request: title_1688='电视机液晶55寸50寸43寸60寸65寸32寸智能wifi网络家用电包邮牌', title_ozon='Samsung Телевизор UE50DU7100UX 50" 4K UHD, черный'
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:20,517 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:20,559 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: INFO:     ************:38738 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:20,715 - __main__ - INFO - Received request body: {"title_1688":"跨境热销led灯带5050led条自粘usb电视背景氛围灯蓝牙带app 遥控","title_ozon":"Hartens Телевизор HTY-50U11B-VS 50\" 4K UHD, черный"}
Jul 30 11:56:20 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:20,716 - __main__ - INFO - POST /product_check - Request: title_1688='跨境热销led灯带5050led条自粘usb电视背景氛围灯蓝牙带app 遥控', title_ozon='Hartens Телевизор HTY-50U11B-VS 50" 4K UHD, черный'
Jul 30 11:56:21 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:21,108 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:21 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:21,151 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:21 VM-4-13-ubuntu python[4034682]: INFO:     ************:38740 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:32 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:32,103 - __main__ - INFO - Received request body: {"title_1688":"高档大气挂画客厅装饰画背有靠山壁画雪山风景画三联叠加画晶瓷画","title_ozon":"Xiaomi Телевизор A 32 2025 32\" HD, черный"}
Jul 30 11:56:32 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:32,104 - __main__ - INFO - POST /product_check - Request: title_1688='高档大气挂画客厅装饰画背有靠山壁画雪山风景画三联叠加画晶瓷画', title_ozon='Xiaomi Телевизор A 32 2025 32" HD, черный'
Jul 30 11:56:33 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:33,067 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:33 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:33,085 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:33 VM-4-13-ubuntu python[4034682]: INFO:     ************:38758 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:56:55 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:55,184 - __main__ - INFO - Received request body: {"title_1688":"Switch2钢化膜Switch OLED护眼紫光钢化膜steam deck保护膜磨砂膜","title_ozon":"Портативная игровая консоль Steam Deck OLED 512ГБ 90Hz"}
Jul 30 11:56:55 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:55,185 - __main__ - INFO - POST /product_check - Request: title_1688='Switch2钢化膜Switch OLED护眼紫光钢化膜steam deck保护膜磨砂膜', title_ozon='Портативная игровая консоль Steam Deck OLED 512ГБ 90Hz'
Jul 30 11:56:55 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:55,620 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:56:55 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:56:55,730 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:56:55 VM-4-13-ubuntu python[4034682]: INFO:     ************:38792 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 11:57:04 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:57:04,211 - __main__ - INFO - Received request body: {"title_1688":"适用三星蓝牙语音电视遥控器BN59-01329A SAMSUNG Voice Remote","title_ozon":"Samsung Телевизор UE50DU8000UX 50\" 4K UHD, черный"}
Jul 30 11:57:04 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:57:04,211 - __main__ - INFO - POST /product_check - Request: title_1688='适用三星蓝牙语音电视遥控器BN59-01329A SAMSUNG Voice Remote', title_ozon='Samsung Телевизор UE50DU8000UX 50" 4K UHD, черный'
Jul 30 11:57:04 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:57:04,659 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 11:57:04 VM-4-13-ubuntu python[4034682]: 2025-07-30 11:57:04,685 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 11:57:04 VM-4-13-ubuntu python[4034682]: INFO:     ************:38834 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:25:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:30,279 - __main__ - INFO - Getting response for process ID: 098556d5-2c93-4a79-b478-ae7e192d8fe6
Jul 30 12:25:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:30,279 - __main__ - INFO - Process status: completed
Jul 30 12:25:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:30,279 - __main__ - INFO - Process completed successfully
Jul 30 12:25:30 VM-4-13-ubuntu python[4034682]: INFO:     ************:40594 - "GET /get_response/098556d5-2c93-4a79-b478-ae7e192d8fe6 HTTP/1.1" 200 OK
Jul 30 12:25:51 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:51,364 - __main__ - INFO - Received request body: {"title_1688":"朝鲜32英寸显示器2k曲面高清无边框32寸台式电脑监控144hz","title_ozon":"Hartens Телевизор HTY-32H11B-VS 32\" HD, черный"}
Jul 30 12:25:51 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:51,365 - __main__ - INFO - POST /product_check - Request: title_1688='朝鲜32英寸显示器2k曲面高清无边框32寸台式电脑监控144hz', title_ozon='Hartens Телевизор HTY-32H11B-VS 32" HD, черный'
Jul 30 12:25:52 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:52,123 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:25:52 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:52,179 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:25:52 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:25:52 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:52,445 - __main__ - INFO - Received request body: {"title_1688":"4K高清电视机65寸家用75寸超薄电视55寸液晶电视100寸智能网络TV","title_ozon":"LG Телевизор OLED55B4RLA 55\" 4K UHD, черный"}
Jul 30 12:25:52 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:52,446 - __main__ - INFO - POST /product_check - Request: title_1688='4K高清电视机65寸家用75寸超薄电视55寸液晶电视100寸智能网络TV', title_ozon='LG Телевизор OLED55B4RLA 55" 4K UHD, черный'
Jul 30 12:25:53 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:53,501 - __main__ - INFO - Received request body: {"title_1688":"打印纸a4整箱批80克70g复印纸500张一包办公用100张一箱4000张","title_ozon":"SvetoCopy 5 шт A4 500 л Бумага для принтера"}
Jul 30 12:25:53 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:53,502 - __main__ - INFO - POST /product_check - Request: title_1688='打印纸a4整箱批80克70g复印纸500张一包办公用100张一箱4000张', title_ozon='SvetoCopy 5 шт A4 500 л Бумага для принтера'
Jul 30 12:25:53 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:53,950 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:25:53 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:53,977 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:25:54 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:54,027 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:25:54 VM-4-13-ubuntu python[4034682]: INFO:     ************:40630 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:25:54 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:54,115 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:25:54 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:25:57 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:57,697 - __main__ - INFO - Received request body: {"title_1688":"适用于Switch2AR膜任天堂OLED/Lite防反光增透膜Switch2 3A电竞膜","title_ozon":"Игровая консоль Nintendo Switch 2 + игра Mario Kart World"}
Jul 30 12:25:57 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:57,698 - __main__ - INFO - POST /product_check - Request: title_1688='适用于Switch2AR膜任天堂OLED/Lite防反光增透膜Switch2 3A电竞膜', title_ozon='Игровая консоль Nintendo Switch 2 + игра Mario Kart World'
Jul 30 12:25:58 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:58,033 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:25:58 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:58,073 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:25:58 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:25:58 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:58,990 - __main__ - INFO - Received request body: {"title_1688":"玩途大工厂直销 滑鼠垫 加大键盘垫 桌垫亚马逊出口畅销 mousepad","title_ozon":"SBER Телевизор SDX-43F2139 43\" Full HD, черный"}
Jul 30 12:25:58 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:58,991 - __main__ - INFO - POST /product_check - Request: title_1688='玩途大工厂直销 滑鼠垫 加大键盘垫 桌垫亚马逊出口畅销 mousepad', title_ozon='SBER Телевизор SDX-43F2139 43" Full HD, черный'
Jul 30 12:25:59 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:59,583 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:25:59 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:25:59,624 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:25:59 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:00 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:00,300 - __main__ - INFO - Received request body: {"title_1688":"跨境充电宝超大容量快充露营灯自带线便携太阳能移动电源3C认证","title_ozon":"RUIPU Внешний аккумулятор, 50000 мАч, 22.5 Вт, черный матовый, K83"}
Jul 30 12:26:00 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:00,301 - __main__ - INFO - POST /product_check - Request: title_1688='跨境充电宝超大容量快充露营灯自带线便携太阳能移动电源3C认证', title_ozon='RUIPU Внешний аккумулятор, 50000 мАч, 22.5 Вт, черный матовый, K83'
Jul 30 12:26:00 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:00,676 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:00 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:00,701 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:26:00 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:01,217 - __main__ - INFO - Received request body: {"title_1688":"客厅装饰画背靠日照金山轻奢大气沙发背景墙壁挂画办公室山水风景","title_ozon":"Hartens Телевизор HTS-32HDR11B-HC24 32\" HD, черный"}
Jul 30 12:26:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:01,218 - __main__ - INFO - POST /product_check - Request: title_1688='客厅装饰画背靠日照金山轻奢大气沙发背景墙壁挂画办公室山水风景', title_ozon='Hartens Телевизор HTS-32HDR11B-HC24 32" HD, черный'
Jul 30 12:26:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:01,549 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:01 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:01,570 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:01 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:02 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:02,821 - __main__ - INFO - Received request body: {"title_1688":"Led screen室内户外全彩led显示屏会议室展厅舞台led屏幕电子屏","title_ozon":"Samsung Телевизор UE43DU7100UX 43\" 4K UHD, черный"}
Jul 30 12:26:02 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:02,822 - __main__ - INFO - POST /product_check - Request: title_1688='Led screen室内户外全彩led显示屏会议室展厅舞台led屏幕电子屏', title_ozon='Samsung Телевизор UE43DU7100UX 43" 4K UHD, черный'
Jul 30 12:26:03 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:03,434 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:03 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:03,462 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:03 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:06 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:06,419 - __main__ - INFO - Received request body: {"title_1688":"特易通MD-UV390 PLUS对讲机加密AES256大功率10W Type-c快充批发","title_ozon":"Рация тут TYT UV390 Plus AES256 3600 mah TYPE-C (IP67/10W)"}
Jul 30 12:26:06 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:06,420 - __main__ - INFO - POST /product_check - Request: title_1688='特易通MD-UV390 PLUS对讲机加密AES256大功率10W Type-c快充批发', title_ozon='Рация тут TYT UV390 Plus AES256 3600 mah TYPE-C (IP67/10W)'
Jul 30 12:26:06 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:06,766 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:06 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:06,833 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:26:06 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:07,880 - __main__ - INFO - Received request body: {"title_1688":"无线WiFi路由器千兆端口高性能穿墙王真wifi6全网通用千兆5G-双频","title_ozon":"Wi-Fi Роутер TP-Link Archer C64"}
Jul 30 12:26:07 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:07,881 - __main__ - INFO - POST /product_check - Request: title_1688='无线WiFi路由器千兆端口高性能穿墙王真wifi6全网通用千兆5G-双频', title_ozon='Wi-Fi Роутер TP-Link Archer C64'
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:08,073 - __main__ - INFO - Received request body: {"title_1688":"适用vivos30手机壳新款电镀AG磨砂s30promini保护套s20自带镜","title_ozon":"OPPO Смартфон A5 Pro Ростест (EAC) 8/256 ГБ, коричневый"}
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:08,076 - __main__ - INFO - POST /product_check - Request: title_1688='适用vivos30手机壳新款电镀AG磨砂s30promini保护套s20自带镜', title_ozon='OPPO Смартфон A5 Pro Ростест (EAC) 8/256 ГБ, коричневый'
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:08,386 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:08,506 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:08,548 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: INFO:     ************:40648 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:08,566 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:26:08 VM-4-13-ubuntu python[4034682]: INFO:     ************:40626 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:14 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:14,766 - __main__ - INFO - Received request body: {"title_1688":"适用小米15PRO钢化膜全屏小米15Ultra防窥曲面防蓝光手机膜全胶","title_ozon":"Poco Смартфон M7 Pro 8/256 ГБ, сиреневый"}
Jul 30 12:26:14 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:14,767 - __main__ - INFO - POST /product_check - Request: title_1688='适用小米15PRO钢化膜全屏小米15Ultra防窥曲面防蓝光手机膜全胶', title_ozon='Poco Смартфон M7 Pro 8/256 ГБ, сиреневый'
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:15,147 - __main__ - INFO - Received request body: {"title_1688":"外贸TV98 ATV机顶盒H313 4K高清蓝牙5GWIFI 网络电视盒子 tv box","title_ozon":"Смарт ТВ приставка для телевизора SharksTraid Mediaroom, Андроид 13, 2 Гб +16 Гб"}
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:15,148 - __main__ - INFO - POST /product_check - Request: title_1688='外贸TV98 ATV机顶盒H313 4K高清蓝牙5GWIFI 网络电视盒子 tv box', title_ozon='Смарт ТВ приставка для телевизора SharksTraid Mediaroom, Андроид 13, 2 Гб +16 Гб'
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:15,170 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:15,206 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: INFO:     ************:40664 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:15,651 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:15,698 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:26:15 VM-4-13-ubuntu python[4034682]: INFO:     ************:40666 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:18 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:18,613 - __main__ - INFO - Received request body: {"title_1688":"新品小米手环9膜水凝膜定位秒贴膜神器手环膜适用小米手环10贴膜","title_ozon":"Xiaomi Фитнес-браслет Smart Band 10, черный"}
Jul 30 12:26:18 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:18,614 - __main__ - INFO - POST /product_check - Request: title_1688='新品小米手环9膜水凝膜定位秒贴膜神器手环膜适用小米手环10贴膜', title_ozon='Xiaomi Фитнес-браслет Smart Band 10, черный'
Jul 30 12:26:18 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:18,640 - __main__ - INFO - Received request body: {"title_1688":"客厅装饰画背靠日照金山轻奢大气沙发背景墙壁挂画办公室山水风景","title_ozon":"Xiaomi Телевизор TV A 43 2025 43\" 4K UHD, черный"}
Jul 30 12:26:18 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:18,641 - __main__ - INFO - POST /product_check - Request: title_1688='客厅装饰画背靠日照金山轻奢大气沙发背景墙壁挂画办公室山水风景', title_ozon='Xiaomi Телевизор TV A 43 2025 43" 4K UHD, черный'
Jul 30 12:26:19 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:19,080 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:19 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:19,116 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:19 VM-4-13-ubuntu python[4034682]: INFO:     ************:40664 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:19 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:19,245 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:19 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:19,257 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:19 VM-4-13-ubuntu python[4034682]: INFO:     ************:40666 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:23 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:23,165 - __main__ - INFO - Received request body: {"title_1688":"白噪音蓝牙音箱音响催助睡眠仪床头舒缓压力无线迷你音响白噪音机","title_ozon":"Яндекс Станция Мини с часами с Алисой Умная колонка на YaGPT, синий сапфир"}
Jul 30 12:26:23 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:23,166 - __main__ - INFO - POST /product_check - Request: title_1688='白噪音蓝牙音箱音响催助睡眠仪床头舒缓压力无线迷你音响白噪音机', title_ozon='Яндекс Станция Мини с часами с Алисой Умная колонка на YaGPT, синий сапфир'
Jul 30 12:26:23 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:23,607 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:23 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:23,608 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:26:23 VM-4-13-ubuntu python[4034682]: INFO:     ************:40666 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:26 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:26,215 - __main__ - INFO - Received request body: {"title_1688":"适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳","title_ozon":"Tecno Смартфон SPARK 30C 8/256 ГБ, черный"}
Jul 30 12:26:26 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:26,216 - __main__ - INFO - POST /product_check - Request: title_1688='适用华为Pura80ultra磁吸手机壳Mate70Pro高透不发黄Nova14超薄壳', title_ozon='Tecno Смартфон SPARK 30C 8/256 ГБ, черный'
Jul 30 12:26:26 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:26,689 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:26 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:26,868 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:26 VM-4-13-ubuntu python[4034682]: INFO:     ************:40666 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:30,704 - __main__ - INFO - Received request body: {"title_1688":"适用传音pova6Neo手机镜头膜摄像头保护膜玻璃镜头贴屏幕膜AR镜头","title_ozon":"Tecno Смартфон TECNO POVA 6 Neo Speed Black Ростест (EAC) 8/128 ГБ, черный"}
Jul 30 12:26:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:30,705 - __main__ - INFO - POST /product_check - Request: title_1688='适用传音pova6Neo手机镜头膜摄像头保护膜玻璃镜头贴屏幕膜AR镜头', title_ozon='Tecno Смартфон TECNO POVA 6 Neo Speed Black Ростест (EAC) 8/128 ГБ, черный'
Jul 30 12:26:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:30,945 - __main__ - INFO - Received request body: {"title_1688":"适用一加ace5秒贴钢化膜防窥膜ace5pro手机膜全屏抗蓝光镜头膜","title_ozon":"realme Смартфон realme Note 60x Ростест (EAC) 3/64 ГБ, зеленый"}
Jul 30 12:26:30 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:30,946 - __main__ - INFO - POST /product_check - Request: title_1688='适用一加ace5秒贴钢化膜防窥膜ace5pro手机膜全屏抗蓝光镜头膜', title_ozon='realme Смартфон realme Note 60x Ростест (EAC) 3/64 ГБ, зеленый'
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:31,216 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:31,231 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: INFO:     ************:40666 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:31,375 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:31,398 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: INFO:     ************:40688 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:31,765 - __main__ - INFO - Received request body: {"title_1688":"客厅装饰画背靠日照金山轻奢大气沙发背景墙壁挂画办公室山水风景","title_ozon":"Xiaomi Телевизор TV A 50 2025 50\" 4K UHD, черный"}
Jul 30 12:26:31 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:31,766 - __main__ - INFO - POST /product_check - Request: title_1688='客厅装饰画背靠日照金山轻奢大气沙发背景墙壁挂画办公室山水风景', title_ozon='Xiaomi Телевизор TV A 50 2025 50" 4K UHD, черный'
Jul 30 12:26:32 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:32,275 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:32 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:32,290 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:32 VM-4-13-ubuntu python[4034682]: INFO:     ************:40688 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:33 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:33,746 - __main__ - INFO - Received request body: {"title_1688":"适用于爱普生标签机耗材标签色带12mm 18mm 24mm标签盒EPON标签带","title_ozon":"Фискальный накопитель 36 месяцев (для маркировки, ФН 1.2, Инвента)"}
Jul 30 12:26:33 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:33,747 - __main__ - INFO - POST /product_check - Request: title_1688='适用于爱普生标签机耗材标签色带12mm 18mm 24mm标签盒EPON标签带', title_ozon='Фискальный накопитель 36 месяцев (для маркировки, ФН 1.2, Инвента)'
Jul 30 12:26:34 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:34,092 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:34 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:34,141 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:34 VM-4-13-ubuntu python[4034682]: INFO:     ************:40688 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:39 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:39,658 - __main__ - INFO - Received request body: {"title_1688":"Switch2钢化膜Switch OLED护眼紫光钢化膜steam deck保护膜磨砂膜","title_ozon":"Портативная игровая консоль Steam Deck OLED 512ГБ 90Hz"}
Jul 30 12:26:39 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:39,659 - __main__ - INFO - POST /product_check - Request: title_1688='Switch2钢化膜Switch OLED护眼紫光钢化膜steam deck保护膜磨砂膜', title_ozon='Портативная игровая консоль Steam Deck OLED 512ГБ 90Hz'
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,138 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,180 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: INFO:     ************:40704 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,278 - __main__ - INFO - Received request body: {"title_1688":"高档大气挂画客厅装饰画背有靠山壁画雪山风景画三联叠加画晶瓷画","title_ozon":"Xiaomi Телевизор A 32 2025 32\" HD, черный"}
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,279 - __main__ - INFO - POST /product_check - Request: title_1688='高档大气挂画客厅装饰画背有靠山壁画雪山风景画三联叠加画晶瓷画', title_ozon='Xiaomi Телевизор A 32 2025 32" HD, черный'
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,521 - __main__ - INFO - Received request body: {"title_1688":"5V3528RGB灯带USB灯带 TV电视背景灯2835USB灯带 跑马灯带套装","title_ozon":"Hartens Телевизор HTY-43U11S-VD 43\" 4K UHD, серебристый"}
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,522 - __main__ - INFO - POST /product_check - Request: title_1688='5V3528RGB灯带USB灯带 TV电视背景灯2835USB灯带 跑马灯带套装', title_ozon='Hartens Телевизор HTY-43U11S-VD 43" 4K UHD, серебристый'
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,646 - __main__ - INFO - Received request body: {"title_1688":"电视机液晶55寸50寸43寸60寸65寸32寸智能wifi网络家用电包邮牌","title_ozon":"Samsung Телевизор UE65DU7100UX 65\" 4K UHD, черный"}
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,647 - __main__ - INFO - POST /product_check - Request: title_1688='电视机液晶55寸50寸43寸60寸65寸32寸智能wifi网络家用电包邮牌', title_ozon='Samsung Телевизор UE65DU7100UX 65" 4K UHD, черный'
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,708 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:40,740 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:40 VM-4-13-ubuntu python[4034682]: INFO:     ************:40706 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:41 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:41,013 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:41 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:41,121 - __main__ - INFO - POST /product_check - Response: {"is_match":true}
Jul 30 12:26:41 VM-4-13-ubuntu python[4034682]: INFO:     ************:40704 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:41 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:41,146 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:41 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:41,185 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:41 VM-4-13-ubuntu python[4034682]: INFO:     ************:40702 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:48 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:48,573 - __main__ - INFO - Received request body: {"title_1688":"适用vivoiqooneo6se钢化膜高清护眼手机膜neo6防摔防窥iqooz6贴膜","title_ozon":"realme Смартфон Note 60X Ростест (EAC) 3/64 ГБ, черный"}
Jul 30 12:26:48 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:48,574 - __main__ - INFO - POST /product_check - Request: title_1688='适用vivoiqooneo6se钢化膜高清护眼手机膜neo6防摔防窥iqooz6贴膜', title_ozon='realme Смартфон Note 60X Ростест (EAC) 3/64 ГБ, черный'
Jul 30 12:26:48 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:48,701 - __main__ - INFO - Received request body: {"title_1688":"储侠监控内存卡 摄像头TF卡适用小米华为萤石海康视频存储卡128G","title_ozon":"TP-Link Tapo C200 IP Камера видеонаблюдения, белая"}
Jul 30 12:26:48 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:48,703 - __main__ - INFO - POST /product_check - Request: title_1688='储侠监控内存卡 摄像头TF卡适用小米华为萤石海康视频存储卡128G', title_ozon='TP-Link Tapo C200 IP Камера видеонаблюдения, белая'
Jul 30 12:26:48 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:48,906 - __main__ - INFO - Received request body: {"title_1688":"适用三星蓝牙语音电视遥控器BN59-01329A SAMSUNG Voice Remote","title_ozon":"Samsung Телевизор UE50DU8000UX 50\" 4K UHD, черный"}
Jul 30 12:26:48 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:48,907 - __main__ - INFO - POST /product_check - Request: title_1688='适用三星蓝牙语音电视遥控器BN59-01329A SAMSUNG Voice Remote', title_ozon='Samsung Телевизор UE50DU8000UX 50" 4K UHD, черный'
Jul 30 12:26:49 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:49,145 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:49 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:49,185 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:49 VM-4-13-ubuntu python[4034682]: INFO:     ************:40724 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:49 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:49,246 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:49 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:49,271 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:49 VM-4-13-ubuntu python[4034682]: INFO:     ************:40722 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:26:50 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:50,050 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:26:50 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:26:50,119 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:26:50 VM-4-13-ubuntu python[4034682]: INFO:     ************:40726 - "POST /product_check HTTP/1.1" 200 OK
Jul 30 12:27:12 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:27:12,626 - __main__ - INFO - Received request body: {"title_1688":"适用于荣耀Honor 400 Lite 5G液态硅胶软保护套纯色简约","title_ozon":"Honor Смартфон 400 Lite Ростест (EAC) 8/256 ГБ, зеленый"}
Jul 30 12:27:12 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:27:12,627 - __main__ - INFO - POST /product_check - Request: title_1688='适用于荣耀Honor 400 Lite 5G液态硅胶软保护套纯色简约', title_ozon='Honor Смартфон 400 Lite Ростест (EAC) 8/256 ГБ, зеленый'
Jul 30 12:27:13 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:27:13,214 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
Jul 30 12:27:13 VM-4-13-ubuntu python[4034682]: 2025-07-30 12:27:13,247 - __main__ - INFO - POST /product_check - Response: {"is_match":false}
Jul 30 12:27:13 VM-4-13-ubuntu python[4034682]: INFO:     ************:40752 - "POST /product_check HTTP/1.1" 200 OK
